import { useCameraNotes } from '@hakimo-ui/hakimo/data-access';
import { CameraDetail } from '@hakimo-ui/hakimo/types';
import { CameraNotesEditor } from '@hakimo-ui/hakimo/feature-shared';
import { HakimoSpinner } from '@hakimo-ui/shared/ui-base';

interface Props {
  camera: CameraDetail;
}

export function CamerDetailNotes(props: Props) {
  const { camera } = props;
  const {
    data: serverNotes = [],
    refetch,
    isLoading,
  } = useCameraNotes(camera.id);

  return (
    <div className="flex h-full">
      <div className="flex-1 pr-6">
        <div className="aspect-video w-full overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800">
          {camera.imageUrl ? (
            <img
              src={camera.imageUrl}
              alt={camera.name}
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="flex h-full items-center justify-center text-gray-500 dark:text-gray-400">
              No image available
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-onlight-text-1 dark:text-ondark-text-1 text-lg font-medium">
            Camera Notes
          </h3>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <HakimoSpinner />
          </div>
        ) : (
          <CameraNotesEditor
            cameraId={camera.id}
            initialNotes={serverNotes}
            onNotesUpdated={refetch}
            className="space-y-4"
          />
        )}
      </div>
    </div>
  );
}

export default CamerDetailNotes;

import { LocationFilterValue, Point } from './common';
import { Location } from './location';

export interface BaseCamera {
  id: string;
  name: string;
}
export interface Camera extends BaseCamera {
  location: Location;
  integrationType: string;
  deadZones?: Point[][];
  vehicleParkingZones?: Point[][];
  activeZones?: Point[][];
  armStatus: ArmStatus;
  isPlaybackEnabled: boolean;
  audioDevices?: CameraAudioDevice[];
  livestreamUrl?: string;
  visionHost?: string;
  visionPlaybackUrl?: string;
  tenantId?: string;
  cameraGroupId?: string;
  cameraGroupName?: string;
}

export interface ArmStatus {
  isArmed: boolean;
  endTimeText?: string;
  endTime?: string;
}
export interface AutomatedTalkdownWindowDTO {
  automatedTalkdownEndTimeUTC: string;
  automatedTalkdownStartTimeUTC: string;
}

export interface CameraAudioDevice {
  id: string;
  deviceType: string;
  url: string;
}

export interface CameraDetail extends Camera {
  imageUrl: string;
  audioDeviceMapped: boolean;
  isMotionDetectionEnabled?: boolean;
  rtspUrl?: string;
  camUserId?: string;
  camPasswordFilePath?: string;
  isEnabled?: boolean;
  automatedTalkdownEndTimeUTC?: string;
  automatedTalkdownStartTimeUTC?: string;
  notes?: string[];
}

export interface CamerasDTO {
  items: Camera[];
  total: number;
}

export interface DoorCameraResponse {
  cameraId: string;
  cameraName: string;
  clientCameraId: string;
  serverUrl: string;
  cameraTimezone: string;
  rawCameraInfo: any;
  rtspUrl: string;
  integrationType: string;
  camSiteId: string;
  tenantId: string;
  livestreamUrl: string;
}

export type CameraUpdateType = 'deadzone' | 'vehicleparkingzone' | 'activezone';

export interface CameraUpdatePayload {
  name?: string;
  enablePlayback?: boolean;
  enableMotionDetection?: boolean;
  livestreamUrl?: string;
  rtspUrl?: string;
  userId?: string;
  passwordFilePath?: string; // @TODO remove this
  isEnabled?: boolean;
  locationId?: string;
}

export type CameraAddPayload = Required<CameraUpdatePayload> & {
  tenantId: string;
};

export interface CameraUpdateDeadZonesPayload {
  deadZones?: { deadZones: Point[][]; labellingResolution: Point };
}

export interface CameraUpdateActiveZonesPayload {
  activeZones?: { activeZones: Point[][]; labellingResolution: Point };
}

export interface CameraUpdateVehicleZonePayload {
  vehicleParkingZones?: {
    vehicleParkingZones: Point[][];
    labellingResolution: Point;
  };
}

export type CameraUpdateZonePayload =
  | CameraUpdateDeadZonesPayload
  | CameraUpdateActiveZonesPayload
  | CameraUpdateVehicleZonePayload;

export interface SetCameraArmedStatusDTO {
  isArmed: boolean;
  activation_window_start: number;
  activation_window_end: number;
}

export type PlaybackMotionMarkers = Array<[number, number]>;

export interface CamFilterValues {
  location: LocationFilterValue;
}

export interface CameraNotesPayload {
  notes: string[];
}

export interface CameraNotesBulkResponse {
  cameraId: string;
  notes: string[];
  cameraName: string;
  cameraImage: string;
}

export interface CameraNotesState {
  cameraId: string;
  cameraName: string;
  notes: string[];
  isEditing: boolean;
  hasChanges: boolean;
}

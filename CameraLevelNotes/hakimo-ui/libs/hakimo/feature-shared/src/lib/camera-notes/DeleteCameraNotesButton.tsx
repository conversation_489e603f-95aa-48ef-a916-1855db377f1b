import { toast } from '@hakimo-ui/hakimo/util';
import { Button } from '@hakimo-ui/shared/ui-base';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useDeleteAllCameraNotes } from './hooks/useDeleteAllCameraNotes';
import { DeleteCameraNotesButtonProps } from './types';

export function DeleteCameraNotesButton({
  cameraId,
  cameraName,
  onSuccess,
}: DeleteCameraNotesButtonProps) {
  const { deleteAllNotes, isLoading } = useDeleteAllCameraNotes(
    cameraId,
    () => {
      toast('All camera notes deleted successfully', { type: 'success' });
      onSuccess();
    }
  );

  const handleDelete = () => {
    if (
      window.confirm(
        `Are you sure you want to remove all notes for camera "${cameraName}"?`
      )
    ) {
      deleteAllNotes();
    }
  };

  return (
    <Button
      variant="icon"
      onClick={handleDelete}
      title="Remove camera notes"
      disabled={isLoading}
    >
      <XMarkIcon className="text-status-red h-5 w-5" />
    </Button>
  );
}

export default DeleteCameraNotesButton;
